import { SipClient, RoomServiceClient } from 'livekit-server-sdk';

// C<PERSON>u hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo clients
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);
const roomService = new RoomServiceClient(livekitUrl, apiKey, apiSecret);

// Cấu hình cuộc gọi
let trunkId = 'ST_pxFYJVBYt5cd';     // ID của outbound trunk (sẽ được tạo tự động nếu cần)
const phoneNumber = '84938709344';  // Số điện thoại cần gọi
const roomName = 'my-sip-room';      // Tên room LiveKit

// C<PERSON><PERSON> hình outbound trunk mẫu (cần thay đổi theo provider thực tế)
const trunkConfig = {
    name: 'Test Outbound Trunk',
    address: 'sip.example.com',  // Thay bằng SIP server của provider
    numbers: ['+**********'],    // Thay bằng số điện thoại của bạn
    authUsername: 'your-username', // Thay bằng username thực tế
    authPassword: 'your-password', // Thay bằng password thực tế
    destinationCountry: 'VN'     // Mã quốc gia
};

// Tùy chọn cho SIP participant theo đúng API specification
const participantOptions = {
    participantIdentity: 'sip-caller-' + Date.now(),
    participantName: 'Test Caller',
    participantMetadata: 'Outbound call test',
    krispEnabled: false,        // Tắt Krisp để tránh lỗi
    waitUntilAnswered: false,   // Không đợi answer để test nhanh hơn
    playDialtone: true,         // Bật dial tone để nghe thấy
    hidePhoneNumber: false
};

// Hàm kiểm tra kết nối LiveKit
async function testConnection() {
    console.log('🔍 Testing LiveKit connection...');
    try {
        const rooms = await roomService.listRooms();
        console.log('✅ LiveKit connection successful!');
        console.log(`📊 Found ${rooms.length} existing rooms`);
        return true;
    } catch (error) {
        console.error('❌ LiveKit connection failed:', error.message);
        return false;
    }
}

// Hàm liệt kê outbound trunks
async function listOutboundTrunks() {
    console.log('\n🔍 Listing outbound trunks...');
    try {
        const trunks = await sipClient.listSipOutboundTrunk();
        console.log(`📋 Found ${trunks.length} outbound trunk(s):`);

        if (trunks.length === 0) {
            console.log('⚠️  No outbound trunks found. You need to create one first.');
            return null;
        }

        trunks.forEach((trunk, index) => {
            console.log(`\n${index + 1}. Trunk ID: ${trunk.sipTrunkId}`);
            console.log(`   Name: ${trunk.name}`);
            console.log(`   Address: ${trunk.address}`);
            console.log(`   Numbers: ${trunk.numbers.join(', ')}`);
            console.log(`   Auth Username: ${trunk.authUsername || 'Not set'}`);
        });

        return trunks[0]; // Trả về trunk đầu tiên
    } catch (error) {
        console.error('❌ Error listing trunks:', error.message);
        return null;
    }
}

// Hàm tạo outbound trunk mẫu (chỉ để demo - cần cấu hình thực tế)
async function createSampleTrunk() {
    console.log('\n🔧 Creating sample outbound trunk...');
    console.log('⚠️  NOTE: This is a demo trunk. You need to configure with real SIP provider details.');

    try {
        const trunk = await sipClient.createSipOutboundTrunk(trunkConfig);
        console.log('✅ Sample trunk created successfully!');
        console.log('Trunk ID:', trunk.sipTrunkId);
        return trunk;
    } catch (error) {
        console.error('❌ Error creating trunk:', error.message);
        return null;
    }
}

// Hàm thực hiện cuộc gọi outbound
async function makeOutboundCall(selectedTrunkId) {
    console.log('\n📞 Making outbound call...');
    console.log('Trunk ID:', selectedTrunkId);
    console.log('Phone number:', phoneNumber);
    console.log('Room name:', roomName);

    try {
        // Tạo room trước để đảm bảo room tồn tại
        console.log('🏠 Creating/ensuring room exists...');
        try {
            await roomService.createRoom({
                name: roomName,
                emptyTimeout: 300, // 5 minutes
                maxParticipants: 10
            });
            console.log('✅ Room created successfully');
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('✅ Room already exists');
            } else {
                console.log('⚠️  Room creation warning:', error.message);
            }
        }

        console.log('📞 Initiating SIP call...');
        const startTime = Date.now();

        const participant = await sipClient.createSipParticipant(
            selectedTrunkId,
            phoneNumber,
            roomName,
            participantOptions
        );

        const duration = Date.now() - startTime;

        console.log(`🎉 SUCCESS! Call initiated in ${duration}ms`);
        console.log('');
        console.log('📋 Call Details:');
        console.log('- Participant ID:', participant.participantId);
        console.log('- Participant Identity:', participant.participantIdentity);
        console.log('- Room Name:', participant.roomName);
        console.log('- SIP Call ID:', participant.sipCallId);
        console.log('');
        console.log('� Call Status:');
        console.log('- The call should now be dialing', phoneNumber);
        console.log('- Check your phone for incoming call');
        console.log('- Join room', roomName, 'in LiveKit to hear the call');

        return participant;

    } catch (error) {
        console.error('❌ Error creating SIP participant:');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);

        // Kiểm tra các lỗi phổ biến
        if (error.message.includes('timeout')) {
            console.error('\n💡 Troubleshooting tips:');
            console.error('- Check your internet connection');
            console.error('- Verify LiveKit server is accessible');
            console.error('- Check if trunk ID is valid and active');
        } else if (error.message.includes('unauthorized') || error.message.includes('403')) {
            console.error('\n💡 Authentication issue:');
            console.error('- Verify API key and secret are correct');
            console.error('- Check if token has SIP call permissions');
        } else if (error.message.includes('trunk')) {
            console.error('\n💡 Trunk configuration issue:');
            console.error('- Verify trunk ID exists and is configured properly');
            console.error('- Check trunk has outbound calling enabled');
        }

        if (error.stack) {
            console.error('\nStack trace:', error.stack);
        }

        return null;
    }
}

// Hàm main chính
async function main() {
    console.log('=== LiveKit SIP Outbound Call Setup & Test ===');
    console.log('LiveKit URL:', livekitUrl);
    console.log('Target phone number:', phoneNumber);
    console.log('Room name:', roomName);
    console.log('');

    // Bước 1: Kiểm tra kết nối
    const connectionOk = await testConnection();
    if (!connectionOk) {
        console.log('\n❌ Cannot proceed without LiveKit connection');
        return;
    }

    // Bước 2: Liệt kê trunks hiện có
    let selectedTrunk = await listOutboundTrunks();

    // Bước 3: Nếu không có trunk, hỏi người dùng có muốn tạo trunk demo không
    if (!selectedTrunk) {
        console.log('\n❓ No outbound trunks found. Options:');
        console.log('1. Create a demo trunk (for testing - will likely fail without real SIP provider)');
        console.log('2. Configure a real trunk via LiveKit Cloud dashboard');
        console.log('\n💡 Recommendation: Set up a real trunk with a SIP provider like Twilio, Telnyx, or Plivo');
        console.log('📖 Guide: https://docs.livekit.io/sip/quickstarts/configuring-sip-trunk/');

        // Tạo demo trunk để test
        selectedTrunk = await createSampleTrunk();
        if (!selectedTrunk) {
            console.log('\n❌ Cannot proceed without a trunk');
            return;
        }
    }

    // Bước 4: Thực hiện cuộc gọi
    const participant = await makeOutboundCall(selectedTrunk.sipTrunkId);

    if (participant) {
        console.log('\n🎉 Call setup completed successfully!');
    } else {
        console.log('\n❌ Call setup failed');
    }
}

console.log('🚀 Starting LiveKit SIP outbound call test...');
main().then(() => {
    console.log('\n✅ Script execution completed');
}).catch((error) => {
    console.error('\n❌ Unhandled error in main:', error);
    process.exit(1);
});