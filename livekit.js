import { SipClient, RoomServiceClient } from 'livekit-server-sdk';

// C<PERSON>u hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo clients
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);
const roomService = new RoomServiceClient(livekitUrl, apiKey, apiSecret);

// Cấu hình cuộc gọi
let trunkId = 'ST_pxFYJVBYt5cd';     // ID của outbound trunk (sẽ được tạo tự động nếu cần)
const phoneNumber = '+84938709344';  // Số điện thoại cần gọi
const roomName = 'my-sip-room';      // Tên room LiveKit

// C<PERSON><PERSON> hình outbound trunk mẫu (cần thay đổi theo provider thực tế)
const trunkConfig = {
    name: 'stringee',
    address: 'v2.stringee.com:15060',  // Stringee SIP server
    numbers: ['+842473030026'],    // Số điện thoại Stringee của bạn
    authUsername: '2424500_thotran20001a2', // Username Stringee
    authPassword: 'Tho123@', // Password Stringee
    destinationCountry: 'VN'     // Mã quốc gia Vietnam
};

// Các format số điện thoại khác nhau để test
const phoneNumberFormats = [
    '+84938709344',    // International format
    '84938709344',     // Without + prefix
    '**********',      // Local format
    '938709344'        // Without leading 0
];

// Tùy chọn cho SIP participant theo đúng API specification
const participantOptions = {
    participantIdentity: 'sip-caller-' + Date.now(),
    participantName: 'Test Caller',
    participantMetadata: 'Outbound call test',
    krispEnabled: false,        // Tắt Krisp để tránh lỗi
    waitUntilAnswered: false,   // Không đợi answer để test nhanh hơn
    playDialtone: true,         // Bật dial tone để nghe thấy
    hidePhoneNumber: false
};

// Hàm kiểm tra kết nối mạng và DNS
async function testNetworkConnectivity() {
    console.log('🌐 Testing network connectivity...');

    try {
        // Test DNS resolution cho Stringee
        const dns = require('dns').promises;
        const stringeeHost = 'v2.stringee.com';

        console.log(`🔍 Resolving DNS for ${stringeeHost}...`);
        const addresses = await dns.resolve4(stringeeHost);
        console.log(`✅ DNS resolved: ${addresses.join(', ')}`);

        // Test kết nối TCP tới Stringee SIP port
        const net = require('net');
        const testConnection = () => {
            return new Promise((resolve, reject) => {
                const socket = new net.Socket();
                const timeout = setTimeout(() => {
                    socket.destroy();
                    reject(new Error('Connection timeout'));
                }, 5000);

                socket.connect(15060, stringeeHost, () => {
                    clearTimeout(timeout);
                    socket.destroy();
                    resolve(true);
                });

                socket.on('error', (err) => {
                    clearTimeout(timeout);
                    reject(err);
                });
            });
        };

        console.log(`🔍 Testing TCP connection to ${stringeeHost}:15060...`);
        await testConnection();
        console.log('✅ TCP connection successful!');

        return true;
    } catch (error) {
        console.error('❌ Network connectivity test failed:', error.message);
        console.error('💡 This might cause SIP 478 errors');
        return false;
    }
}

// Hàm kiểm tra kết nối LiveKit
async function testConnection() {
    console.log('🔍 Testing LiveKit connection...');
    try {
        const rooms = await roomService.listRooms();
        console.log('✅ LiveKit connection successful!');
        console.log(`📊 Found ${rooms.length} existing rooms`);
        return true;
    } catch (error) {
        console.error('❌ LiveKit connection failed:', error.message);
        return false;
    }
}

// Hàm liệt kê outbound trunks
async function listOutboundTrunks() {
    console.log('\n🔍 Listing outbound trunks...');
    try {
        const trunks = await sipClient.listSipOutboundTrunk();
        console.log(`📋 Found ${trunks.length} outbound trunk(s):`);

        if (trunks.length === 0) {
            console.log('⚠️  No outbound trunks found. You need to create one first.');
            return null;
        }

        trunks.forEach((trunk, index) => {
            console.log(`\n${index + 1}. Trunk ID: ${trunk.sipTrunkId}`);
            console.log(`   Name: ${trunk.name}`);
            console.log(`   Address: ${trunk.address}`);
            console.log(`   Numbers: ${trunk.numbers.join(', ')}`);
            console.log(`   Auth Username: ${trunk.authUsername || 'Not set'}`);
        });

        return trunks[0]; // Trả về trunk đầu tiên
    } catch (error) {
        console.error('❌ Error listing trunks:', error.message);
        return null;
    }
}

// Hàm kiểm tra và cập nhật trunk configuration
async function updateTrunkConfiguration(trunkId) {
    console.log('\n🔧 Checking and updating trunk configuration...');

    try {
        // Lấy thông tin trunk hiện tại
        const trunks = await sipClient.listSipOutboundTrunk();
        const currentTrunk = trunks.find(t => t.sipTrunkId === trunkId);

        if (!currentTrunk) {
            console.error('❌ Trunk not found:', trunkId);
            return null;
        }

        console.log('📋 Current trunk configuration:');
        console.log('- Name:', currentTrunk.name);
        console.log('- Address:', currentTrunk.address);
        console.log('- Numbers:', currentTrunk.numbers);
        console.log('- Auth Username:', currentTrunk.authUsername);

        // Kiểm tra các vấn đề phổ biến
        const issues = [];

        if (!currentTrunk.address || !currentTrunk.address.includes(':')) {
            issues.push('SIP address missing port number');
        }

        if (!currentTrunk.authUsername || !currentTrunk.authPassword) {
            issues.push('Missing authentication credentials');
        }

        if (!currentTrunk.numbers || currentTrunk.numbers.length === 0) {
            issues.push('No phone numbers configured');
        }

        if (issues.length > 0) {
            console.log('\n⚠️  Configuration issues found:');
            issues.forEach(issue => console.log('- ' + issue));

            console.log('\n🔧 Attempting to update trunk configuration...');

            // Cập nhật trunk với cấu hình mới
            const updatedConfig = {
                ...trunkConfig,
                sipTrunkId: trunkId
            };

            try {
                const updatedTrunk = await sipClient.updateSipOutboundTrunk(updatedConfig);
                console.log('✅ Trunk configuration updated successfully!');
                return updatedTrunk;
            } catch (updateError) {
                console.error('❌ Failed to update trunk:', updateError.message);
                console.log('💡 You may need to update trunk configuration manually in LiveKit dashboard');
            }
        } else {
            console.log('✅ Trunk configuration looks good');
        }

        return currentTrunk;

    } catch (error) {
        console.error('❌ Error checking trunk configuration:', error.message);
        return null;
    }
}

// Hàm tạo outbound trunk mẫu (chỉ để demo - cần cấu hình thực tế)
async function createSampleTrunk() {
    console.log('\n🔧 Creating sample outbound trunk...');
    console.log('⚠️  NOTE: This is a demo trunk. You need to configure with real SIP provider details.');

    try {
        const trunk = await sipClient.createSipOutboundTrunk(trunkConfig);
        console.log('✅ Sample trunk created successfully!');
        console.log('Trunk ID:', trunk.sipTrunkId);
        return trunk;
    } catch (error) {
        console.error('❌ Error creating trunk:', error.message);
        return null;
    }
}

// Hàm kiểm tra trạng thái cuộc gọi
async function checkCallStatus(participantId, sipCallId) {
    console.log('\n🔍 Checking call status...');
    try {
        // Lấy thông tin participant
        const participants = await roomService.listParticipants(roomName);
        const participant = participants.find(p => p.identity === participantId);

        if (participant) {
            console.log('✅ Participant found in room');
            console.log('- State:', participant.state);
            console.log('- Joined at:', new Date(participant.joinedAt * 1000).toLocaleString());
            console.log('- Connection quality:', participant.connectionQuality);
        } else {
            console.log('❌ Participant not found in room');
        }

        // Lấy thông tin SIP call nếu có API
        if (sipCallId) {
            console.log('- SIP Call ID:', sipCallId);
            // Note: LiveKit có thể không có API public để check SIP call status
        }

        return participant;
    } catch (error) {
        console.error('❌ Error checking call status:', error.message);
        return null;
    }
}

// Hàm test với nhiều format số điện thoại
async function testPhoneNumberFormats(selectedTrunkId) {
    console.log('\n🧪 Testing different phone number formats...');

    for (let i = 0; i < phoneNumberFormats.length; i++) {
        const testNumber = phoneNumberFormats[i];
        console.log(`\n📞 Testing format ${i + 1}: ${testNumber}`);

        try {
            const testParticipant = await sipClient.createSipParticipant(
                selectedTrunkId,
                testNumber,
                `test-room-${i}`,
                {
                    ...participantOptions,
                    participantIdentity: `test-caller-${Date.now()}-${i}`,
                    waitUntilAnswered: false
                }
            );

            console.log(`✅ Format ${testNumber} accepted by LiveKit`);
            console.log('- Participant ID:', testParticipant.participantId);
            console.log('- SIP Call ID:', testParticipant.sipCallId);

            // Đợi một chút để kiểm tra trạng thái
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Kiểm tra trạng thái cuộc gọi
            await checkCallStatus(testParticipant.participantIdentity, testParticipant.sipCallId);

            return testParticipant; // Trả về participant đầu tiên thành công

        } catch (error) {
            console.error(`❌ Format ${testNumber} failed:`, error.message);

            // Phân tích lỗi cụ thể
            if (error.message.includes('478')) {
                console.error('   → SIP 478: Unresolvable destination - Invalid phone number format or routing issue');
            } else if (error.message.includes('404')) {
                console.error('   → SIP 404: Not found - Number not reachable or invalid');
            } else if (error.message.includes('403')) {
                console.error('   → SIP 403: Forbidden - Authentication or permission issue');
            }
        }
    }

    return null;
}

// Hàm thực hiện cuộc gọi outbound với debug nâng cao
async function makeOutboundCall(selectedTrunkId) {
    console.log('\n📞 Making outbound call...');
    console.log('Trunk ID:', selectedTrunkId);
    console.log('Phone number:', phoneNumber);
    console.log('Room name:', roomName);

    try {
        // Tạo room trước để đảm bảo room tồn tại
        console.log('🏠 Creating/ensuring room exists...');
        try {
            await roomService.createRoom({
                name: roomName,
                emptyTimeout: 300, // 5 minutes
                maxParticipants: 10
            });
            console.log('✅ Room created successfully');
        } catch (error) {
            if (error.message.includes('already exists')) {
                console.log('✅ Room already exists');
            } else {
                console.log('⚠️  Room creation warning:', error.message);
            }
        }

        console.log('📞 Initiating SIP call...');
        const startTime = Date.now();

        const participant = await sipClient.createSipParticipant(
            selectedTrunkId,
            phoneNumber,
            roomName,
            participantOptions
        );

        const duration = Date.now() - startTime;

        console.log(`🎉 SUCCESS! Call initiated in ${duration}ms`);
        console.log('');
        console.log('📋 Call Details:');
        console.log('- Participant ID:', participant.participantId);
        console.log('- Participant Identity:', participant.participantIdentity);
        console.log('- Room Name:', participant.roomName);
        console.log('- SIP Call ID:', participant.sipCallId);
        console.log('');
        console.log('📞 Call Status:');
        console.log('- The call should now be dialing', phoneNumber);
        console.log('- Check your phone for incoming call');
        console.log('- Join room', roomName, 'in LiveKit to hear the call');

        // Đợi một chút rồi kiểm tra trạng thái
        console.log('\n⏳ Waiting 3 seconds before checking call status...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        await checkCallStatus(participant.participantIdentity, participant.sipCallId);

        return participant;

    } catch (error) {
        console.error('❌ Error creating SIP participant:');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);

        // Phân tích lỗi SIP cụ thể
        if (error.message.includes('478')) {
            console.error('\n🔍 SIP 478 Error Analysis:');
            console.error('- "Unresolvable destination" - The phone number cannot be routed');
            console.error('- Possible causes:');
            console.error('  • Invalid phone number format');
            console.error('  • Number not reachable from your SIP provider');
            console.error('  • Trunk routing configuration issue');
            console.error('  • Country/region restrictions');
            console.error('\n💡 Trying different phone number formats...');

            // Test với các format khác nhau
            const testResult = await testPhoneNumberFormats(selectedTrunkId);
            if (testResult) {
                console.log('\n✅ Found working format! Using:', testResult);
                return testResult;
            }
        } else if (error.message.includes('timeout')) {
            console.error('\n💡 Troubleshooting tips:');
            console.error('- Check your internet connection');
            console.error('- Verify LiveKit server is accessible');
            console.error('- Check if trunk ID is valid and active');
        } else if (error.message.includes('unauthorized') || error.message.includes('403')) {
            console.error('\n💡 Authentication issue:');
            console.error('- Verify API key and secret are correct');
            console.error('- Check if token has SIP call permissions');
        } else if (error.message.includes('trunk')) {
            console.error('\n💡 Trunk configuration issue:');
            console.error('- Verify trunk ID exists and is configured properly');
            console.error('- Check trunk has outbound calling enabled');
        }

        if (error.stack) {
            console.error('\nStack trace:', error.stack);
        }

        return null;
    }
}

// Hàm main chính
async function main() {
    console.log('=== LiveKit SIP Outbound Call Setup & Test ===');
    console.log('LiveKit URL:', livekitUrl);
    console.log('Target phone number:', phoneNumber);
    console.log('Room name:', roomName);
    console.log('');

    // Bước 1: Kiểm tra kết nối
    const connectionOk = await testConnection();
    if (!connectionOk) {
        console.log('\n❌ Cannot proceed without LiveKit connection');
        return;
    }

    // Bước 2: Liệt kê trunks hiện có
    let selectedTrunk = await listOutboundTrunks();

    // Bước 3: Nếu không có trunk, hỏi người dùng có muốn tạo trunk demo không
    if (!selectedTrunk) {
        console.log('\n❓ No outbound trunks found. Options:');
        console.log('1. Create a demo trunk (for testing - will likely fail without real SIP provider)');
        console.log('2. Configure a real trunk via LiveKit Cloud dashboard');
        console.log('\n💡 Recommendation: Set up a real trunk with a SIP provider like Twilio, Telnyx, or Plivo');
        console.log('📖 Guide: https://docs.livekit.io/sip/quickstarts/configuring-sip-trunk/');

        // Tạo demo trunk để test
        selectedTrunk = await createSampleTrunk();
        if (!selectedTrunk) {
            console.log('\n❌ Cannot proceed without a trunk');
            return;
        }
    }

    // Bước 3.5: Kiểm tra và cập nhật cấu hình trunk
    console.log('\n🔍 Verifying trunk configuration...');
    const verifiedTrunk = await updateTrunkConfiguration(selectedTrunk.sipTrunkId);
    if (verifiedTrunk) {
        selectedTrunk = verifiedTrunk;
    }

    // Bước 4: Thực hiện cuộc gọi
    const participant = await makeOutboundCall(selectedTrunk.sipTrunkId);

    if (participant) {
        console.log('\n🎉 Call setup completed successfully!');
    } else {
        console.log('\n❌ Call setup failed');
    }
}

console.log('🚀 Starting LiveKit SIP outbound call test...');
main().then(() => {
    console.log('\n✅ Script execution completed');
}).catch((error) => {
    console.error('\n❌ Unhandled error in main:', error);
    process.exit(1);
});