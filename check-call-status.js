import { SipClient, RoomServiceClient } from 'livekit-server-sdk';

// <PERSON><PERSON><PERSON> hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo clients
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);
const roomService = new RoomServiceClient(livekitUrl, apiKey, apiSecret);

const roomName = 'my-sip-room';

// Hàm kiểm tra trạng thái chi tiết
async function checkDetailedCallStatus() {
    console.log('🔍 Checking detailed call status...');
    console.log('Room name:', roomName);
    
    try {
        // Kiểm tra room có tồn tại không
        console.log('\n📋 Checking room existence...');
        const rooms = await roomService.listRooms();
        const targetRoom = rooms.find(room => room.name === roomName);
        
        if (!targetRoom) {
            console.log('❌ Room not found');
            return;
        }
        
        console.log('✅ Room found:');
        console.log('- Room SID:', targetRoom.sid);
        console.log('- Created at:', new Date(targetRoom.creationTime * 1000).toLocaleString());
        console.log('- Empty timeout:', targetRoom.emptyTimeout);
        console.log('- Max participants:', targetRoom.maxParticipants);
        console.log('- Num participants:', targetRoom.numParticipants);
        console.log('- Num publishers:', targetRoom.numPublishers);
        
        // Liệt kê tất cả participants trong room
        console.log('\n👥 Listing participants...');
        const participants = await roomService.listParticipants(roomName);
        
        if (participants.length === 0) {
            console.log('❌ No participants found in room');
            console.log('💡 This suggests the SIP call may have failed or disconnected');
        } else {
            console.log(`✅ Found ${participants.length} participant(s):`);
            
            participants.forEach((participant, index) => {
                console.log(`\n${index + 1}. Participant:`);
                console.log('   - Identity:', participant.identity);
                console.log('   - Name:', participant.name || 'N/A');
                console.log('   - State:', participant.state);
                console.log('   - Joined at:', new Date(participant.joinedAt * 1000).toLocaleString());
                console.log('   - Is publisher:', participant.isPublisher);
                console.log('   - Connection quality:', participant.connectionQuality);
                console.log('   - Metadata:', participant.metadata || 'N/A');
                
                // Kiểm tra tracks
                if (participant.tracks && participant.tracks.length > 0) {
                    console.log('   - Tracks:');
                    participant.tracks.forEach(track => {
                        console.log(`     • ${track.type}: ${track.name} (${track.source})`);
                    });
                } else {
                    console.log('   - No tracks');
                }
            });
        }
        
        // Kiểm tra SIP trunks
        console.log('\n📞 Checking SIP trunks...');
        const trunks = await sipClient.listSipOutboundTrunk();
        
        if (trunks.length > 0) {
            console.log(`✅ Found ${trunks.length} SIP trunk(s):`);
            trunks.forEach((trunk, index) => {
                console.log(`\n${index + 1}. Trunk:`);
                console.log('   - ID:', trunk.sipTrunkId);
                console.log('   - Name:', trunk.name);
                console.log('   - Address:', trunk.address);
                console.log('   - Numbers:', trunk.numbers.join(', '));
                console.log('   - Auth Username:', trunk.authUsername);
            });
        } else {
            console.log('❌ No SIP trunks found');
        }
        
    } catch (error) {
        console.error('❌ Error checking call status:', error.message);
        if (error.stack) {
            console.error('Stack trace:', error.stack);
        }
    }
}

// Hàm kiểm tra logs (nếu có API)
async function checkLogs() {
    console.log('\n📝 Checking for recent logs...');
    
    try {
        // LiveKit có thể không có public API để lấy logs
        // Nhưng chúng ta có thể thử một số cách khác
        console.log('💡 Log checking not available via API');
        console.log('💡 Check LiveKit Cloud dashboard for detailed logs');
        console.log('💡 URL: https://cloud.livekit.io/');
        
    } catch (error) {
        console.error('❌ Error checking logs:', error.message);
    }
}

// Hàm thử gọi lại với cấu hình khác
async function retryCallWithDifferentConfig() {
    console.log('\n🔄 Attempting retry call with different configuration...');
    
    const phoneNumber = '+84938709344';
    const retryRoomName = 'retry-sip-room-' + Date.now();
    
    try {
        // Lấy trunk ID
        const trunks = await sipClient.listSipOutboundTrunk();
        if (trunks.length === 0) {
            console.log('❌ No trunks available for retry');
            return;
        }
        
        const trunkId = trunks[0].sipTrunkId;
        
        // Tạo room mới
        await roomService.createRoom({
            name: retryRoomName,
            emptyTimeout: 600, // 10 minutes
            maxParticipants: 5
        });
        
        console.log('✅ Retry room created:', retryRoomName);
        
        // Thử với cấu hình khác
        const retryOptions = {
            participantIdentity: 'retry-caller-' + Date.now(),
            participantName: 'Retry Test Call',
            participantMetadata: 'Retry attempt',
            krispEnabled: false,
            waitUntilAnswered: true,  // Đợi answer
            playDialtone: true,
            hidePhoneNumber: false
        };
        
        console.log('📞 Making retry call...');
        console.log('- Phone:', phoneNumber);
        console.log('- Room:', retryRoomName);
        console.log('- Wait until answered:', retryOptions.waitUntilAnswered);
        
        const participant = await sipClient.createSipParticipant(
            trunkId,
            phoneNumber,
            retryRoomName,
            retryOptions
        );
        
        console.log('🎉 Retry call initiated successfully!');
        console.log('- Participant ID:', participant.participantId);
        console.log('- SIP Call ID:', participant.sipCallId);
        
        // Đợi lâu hơn để kiểm tra
        console.log('\n⏳ Waiting 10 seconds for call to establish...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // Kiểm tra trạng thái
        const retryParticipants = await roomService.listParticipants(retryRoomName);
        if (retryParticipants.length > 0) {
            console.log('✅ Retry call participant found in room!');
            retryParticipants.forEach(p => {
                console.log('- Identity:', p.identity);
                console.log('- State:', p.state);
                console.log('- Connection quality:', p.connectionQuality);
            });
        } else {
            console.log('❌ Retry call participant still not found');
        }
        
    } catch (error) {
        console.error('❌ Retry call failed:', error.message);
        
        if (error.message.includes('478')) {
            console.error('\n🔍 Still getting SIP 478 error');
            console.error('💡 Possible solutions:');
            console.error('1. Check if phone number format is correct for your SIP provider');
            console.error('2. Verify SIP trunk routing configuration');
            console.error('3. Check if destination country/region is supported');
            console.error('4. Verify authentication credentials');
            console.error('5. Contact your SIP provider for routing issues');
        }
    }
}

// Main function
async function main() {
    console.log('🔍 LiveKit SIP Call Status Checker');
    console.log('=====================================\n');
    
    await checkDetailedCallStatus();
    await checkLogs();
    await retryCallWithDifferentConfig();
    
    console.log('\n✅ Status check completed');
}

main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
});
