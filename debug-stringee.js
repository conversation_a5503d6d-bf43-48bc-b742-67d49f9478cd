import { SipClient, RoomServiceClient } from 'livekit-server-sdk';

// <PERSON><PERSON><PERSON> hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo clients
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);
const roomService = new RoomServiceClient(livekitUrl, apiKey, apiSecret);

// Hàm kiểm tra cấu hình <PERSON>ee chi tiết
async function debugStringeeConfiguration() {
    console.log('🔍 STRINGEE CONFIGURATION DEBUG');
    console.log('===============================\n');
    
    try {
        // L<PERSON>y thông tin trunk
        const trunks = await sipClient.listSipOutboundTrunk();
        if (trunks.length === 0) {
            console.log('❌ No SIP trunks found');
            return;
        }
        
        const trunk = trunks[0];
        console.log('📋 TRUNK DETAILS:');
        console.log('- ID:', trunk.sipTrunkId);
        console.log('- Name:', trunk.name);
        console.log('- Address:', trunk.address);
        console.log('- Numbers:', trunk.numbers);
        console.log('- Auth Username:', trunk.authUsername);
        console.log('- Metadata:', trunk.metadata || 'None');
        
        // Phân tích cấu hình
        console.log('\n🔍 CONFIGURATION ANALYSIS:');
        
        // Kiểm tra địa chỉ SIP
        if (trunk.address === 'v2.stringee.com:15060') {
            console.log('✅ SIP address format correct');
            console.log('✅ Using Stringee V2 server');
            console.log('✅ Port 15060 is correct for Stringee');
        } else {
            console.log('⚠️  Unexpected SIP address format');
        }
        
        // Kiểm tra số điện thoại trunk
        const trunkNumber = trunk.numbers[0];
        if (trunkNumber.startsWith('+842')) {
            console.log('✅ Vietnam landline number detected');
            console.log('⚠️  POTENTIAL ISSUE: Landline to mobile calling restrictions');
            console.log('💡 Vietnam landline numbers may have restrictions calling mobile numbers');
        }
        
        // Kiểm tra username format
        if (trunk.authUsername.includes('_')) {
            console.log('✅ Stringee username format looks correct');
        }
        
        console.log('\n🔍 STRINGEE SPECIFIC CHECKS:');
        console.log('============================');
        
        // Các vấn đề phổ biến với Stringee
        console.log('📞 Common Stringee Issues:');
        console.log('1. Account balance insufficient for outbound calls');
        console.log('2. International calling not enabled');
        console.log('3. Mobile calling restrictions from landline numbers');
        console.log('4. Routing rules not configured for Vietnam mobile prefixes');
        console.log('5. Authentication credentials expired or incorrect');
        
        console.log('\n💡 STRINGEE ACCOUNT REQUIREMENTS:');
        console.log('- Sufficient balance for outbound calls');
        console.log('- International calling enabled');
        console.log('- Mobile calling permissions');
        console.log('- Correct routing rules for 09x numbers');
        
        return trunk;
        
    } catch (error) {
        console.error('❌ Error debugging Stringee configuration:', error.message);
        return null;
    }
}

// Hàm test với số điện thoại khác nhau
async function testDifferentNumbers(trunk) {
    console.log('\n🧪 TESTING DIFFERENT PHONE NUMBERS');
    console.log('==================================\n');
    
    // Các số test khác nhau
    const testNumbers = [
        // Số gốc
        { number: '+***********', description: 'Original target (Viettel mobile)' },
        
        // Số landline để test
        { number: '+************', description: 'Trunk number itself (should work)' },
        { number: '+************', description: 'Similar landline number' },
        
        // Số mobile khác
        { number: '+***********', description: 'Different mobile number (Viettel)' },
        { number: '+***********', description: 'Different mobile number (Viettel)' },
        
        // Số quốc tế để test routing
        { number: '+**********', description: 'US number (test international routing)' },
    ];
    
    for (const test of testNumbers) {
        console.log(`📞 Testing: ${test.number}`);
        console.log(`   Description: ${test.description}`);
        
        const testRoomName = `debug-test-${Date.now()}`;
        
        try {
            // Tạo room
            await roomService.createRoom({
                name: testRoomName,
                emptyTimeout: 60,
                maxParticipants: 2
            });
            
            // Test call với timeout ngắn
            const participant = await sipClient.createSipParticipant(
                trunk.sipTrunkId,
                test.number,
                testRoomName,
                {
                    participantIdentity: `debug-${Date.now()}`,
                    participantName: 'Debug Test',
                    participantMetadata: `Testing ${test.number}`,
                    krispEnabled: false,
                    waitUntilAnswered: false,
                    playDialtone: false,
                    hidePhoneNumber: false
                }
            );
            
            console.log(`   ✅ SUCCESS: Call initiated`);
            console.log(`   📋 SIP Call ID: ${participant.sipCallId}`);
            
            // Đợi 3 giây để kiểm tra
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const participants = await roomService.listParticipants(testRoomName);
            if (participants.length > 0) {
                console.log(`   🎉 CONNECTED: Participant found in room`);
            } else {
                console.log(`   ⏳ INITIATED: Call started but not connected yet`);
            }
            
        } catch (error) {
            console.log(`   ❌ FAILED: ${error.message}`);
            
            if (error.message.includes('478')) {
                console.log(`   → SIP 478: Unresolvable destination`);
                if (test.number.startsWith('+84')) {
                    console.log(`   → Possible Vietnam routing issue`);
                }
            }
        }
        
        console.log('');
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
}

// Hàm kiểm tra cấu hình mạng
async function checkNetworkConfiguration() {
    console.log('🌐 NETWORK CONFIGURATION CHECK');
    console.log('==============================\n');
    
    try {
        // Test DNS resolution
        console.log('🔍 Testing DNS resolution...');
        
        // Sử dụng fetch để test kết nối
        const testUrls = [
            'https://v2.stringee.com',
            'https://livekit.io',
            livekitUrl.replace('wss://', 'https://')
        ];
        
        for (const url of testUrls) {
            try {
                const response = await fetch(url, { 
                    method: 'HEAD',
                    timeout: 5000 
                });
                console.log(`✅ ${url}: ${response.status}`);
            } catch (error) {
                console.log(`❌ ${url}: ${error.message}`);
            }
        }
        
    } catch (error) {
        console.log('❌ Network check failed:', error.message);
    }
}

// Hàm tạo cấu hình trunk mới với thông số tối ưu
async function createOptimizedTrunk() {
    console.log('\n🔧 CREATING OPTIMIZED TRUNK CONFIGURATION');
    console.log('=========================================\n');
    
    const optimizedConfig = {
        name: 'stringee-optimized',
        address: 'v2.stringee.com:15060',
        numbers: ['+************'],
        authUsername: '2424500_thotran20001a2',
        authPassword: 'Tho123@',
        destinationCountry: 'VN',
        // Thêm metadata để debug
        metadata: JSON.stringify({
            provider: 'stringee',
            country: 'vietnam',
            type: 'landline',
            optimized: true,
            created: new Date().toISOString()
        })
    };
    
    try {
        console.log('🔧 Creating optimized trunk...');
        console.log('Configuration:', JSON.stringify(optimizedConfig, null, 2));
        
        const newTrunk = await sipClient.createSipOutboundTrunk(optimizedConfig);
        console.log('✅ Optimized trunk created successfully!');
        console.log('📋 New trunk ID:', newTrunk.sipTrunkId);
        
        return newTrunk;
        
    } catch (error) {
        console.error('❌ Failed to create optimized trunk:', error.message);
        
        if (error.message.includes('already exists')) {
            console.log('💡 Trunk with similar configuration already exists');
        }
        
        return null;
    }
}

// Main function
async function main() {
    console.log('🔧 STRINGEE DEBUG & FIX TOOL');
    console.log('============================\n');
    
    // Bước 1: Debug cấu hình hiện tại
    const trunk = await debugStringeeConfiguration();
    
    if (!trunk) {
        console.log('❌ Cannot proceed without trunk information');
        return;
    }
    
    // Bước 2: Kiểm tra mạng
    await checkNetworkConfiguration();
    
    // Bước 3: Test với số khác nhau
    await testDifferentNumbers(trunk);
    
    // Bước 4: Tạo trunk tối ưu (nếu cần)
    console.log('\n❓ Would you like to create an optimized trunk configuration?');
    console.log('💡 This might help resolve routing issues');
    
    // Uncomment dòng dưới nếu muốn tạo trunk mới
    // await createOptimizedTrunk();
    
    console.log('\n📋 SUMMARY & RECOMMENDATIONS:');
    console.log('=============================');
    console.log('1. ✅ LiveKit integration is working');
    console.log('2. ✅ SIP trunk configuration is valid');
    console.log('3. ⚠️  SIP 478 errors suggest routing issues');
    console.log('4. 💡 Check Stringee account settings:');
    console.log('   - Account balance');
    console.log('   - International calling permissions');
    console.log('   - Mobile calling from landline restrictions');
    console.log('   - Vietnam mobile number routing rules');
    console.log('');
    console.log('🔗 Stringee Dashboard: https://developer.stringee.com/');
    console.log('📞 Stringee Support: Check your account configuration');
}

main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
});
