import { SipClient, RoomServiceClient } from 'livekit-server-sdk';

// C<PERSON>u hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo clients
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);
const roomService = new RoomServiceClient(livekitUrl, apiKey, apiSecret);

// Cấu hình cuộc gọi
const phoneNumber = '+***********';
const roomName = 'final-test-' + Date.now();

async function finalCallTest() {
    console.log('🎯 FINAL CALL TEST - OPTIMIZED CONFIGURATION');
    console.log('=============================================');
    console.log('📱 Target phone:', phoneNumber);
    console.log('🏠 Room name:', roomName);
    console.log('⏰ Time:', new Date().toLocaleString());
    console.log('');
    
    try {
        // Lấy trunk
        const trunks = await sipClient.listSipOutboundTrunk();
        const trunk = trunks[0];
        
        console.log('📞 Using trunk:', trunk.name);
        console.log('📞 From number:', trunk.numbers[0]);
        console.log('');
        
        // Tạo room
        console.log('🏠 Creating room...');
        await roomService.createRoom({
            name: roomName,
            emptyTimeout: 300, // 5 minutes
            maxParticipants: 3
        });
        console.log('✅ Room created');
        
        // Cấu hình tối ưu để tránh lỗi 478
        const optimizedOptions = {
            participantIdentity: 'final-test-' + Date.now(),
            participantName: 'Final Test Call',
            participantMetadata: 'Optimized configuration test',
            krispEnabled: false,
            waitUntilAnswered: false,  // KHÔNG đợi answer để tránh timeout
            playDialtone: true,
            hidePhoneNumber: false
        };
        
        console.log('📞 Making call with optimized settings...');
        console.log('⚙️  Configuration:');
        console.log('   - Wait until answered: FALSE (to avoid timeout)');
        console.log('   - Play dial tone: TRUE');
        console.log('   - Krisp noise reduction: FALSE');
        console.log('');
        console.log('🔔 PLEASE CHECK YOUR PHONE NOW!');
        console.log('📱 Incoming call from:', trunk.numbers[0]);
        console.log('');
        
        const startTime = Date.now();
        
        const participant = await sipClient.createSipParticipant(
            trunk.sipTrunkId,
            phoneNumber,
            roomName,
            optimizedOptions
        );
        
        const initTime = Date.now() - startTime;
        
        console.log('🎉 SUCCESS! Call initiated in', initTime + 'ms');
        console.log('📋 Details:');
        console.log('   - Participant ID:', participant.participantId);
        console.log('   - SIP Call ID:', participant.sipCallId);
        console.log('   - Room name:', participant.roomName);
        console.log('');
        
        // Monitoring cuộc gọi
        console.log('📊 CALL MONITORING:');
        console.log('==================');
        
        let callConnected = false;
        let monitoringCount = 0;
        const maxMonitoring = 12; // 60 seconds total
        
        while (monitoringCount < maxMonitoring && !callConnected) {
            monitoringCount++;
            const waitTime = 5;
            
            console.log(`⏳ Check ${monitoringCount}/${maxMonitoring} - waiting ${waitTime}s...`);
            await new Promise(resolve => setTimeout(resolve, waitTime * 1000));
            
            try {
                const participants = await roomService.listParticipants(roomName);
                const sipParticipant = participants.find(p => p.identity === participant.participantIdentity);
                
                if (sipParticipant) {
                    console.log('✅ CALL CONNECTED!');
                    console.log('📊 Status:');
                    console.log('   - State:', sipParticipant.state);
                    console.log('   - Connection quality:', sipParticipant.connectionQuality);
                    console.log('   - Is publisher:', sipParticipant.isPublisher);
                    console.log('   - Joined at:', new Date(Number(sipParticipant.joinedAt) * 1000).toLocaleString());
                    
                    if (sipParticipant.tracks && sipParticipant.tracks.length > 0) {
                        console.log('   - Tracks available:', sipParticipant.tracks.length);
                        sipParticipant.tracks.forEach(track => {
                            console.log(`     • ${track.type}: ${track.name}`);
                        });
                    }
                    
                    callConnected = true;
                    
                    console.log('');
                    console.log('🎉 CALL TEST SUCCESSFUL!');
                    console.log('========================');
                    console.log('✅ SIP 478 error resolved');
                    console.log('✅ Phone number format correct');
                    console.log('✅ Call successfully connected');
                    console.log('✅ LiveKit integration working');
                    console.log('');
                    console.log('📞 What you can do now:');
                    console.log('1. Answer the phone to hear audio');
                    console.log('2. Join LiveKit room to participate in call');
                    console.log('3. Use this configuration for production calls');
                    
                    break;
                } else {
                    console.log(`   ⏳ Still connecting... (${monitoringCount * waitTime}s elapsed)`);
                    
                    // Thông báo đặc biệt tại các mốc thời gian
                    if (monitoringCount === 3) { // 15s
                        console.log('   💡 Call should be ringing on your phone now');
                    } else if (monitoringCount === 6) { // 30s
                        console.log('   ❓ Did you receive the call? Answer if ringing');
                    } else if (monitoringCount === 9) { // 45s
                        console.log('   ⚠️  Call may have timed out or gone to voicemail');
                    }
                }
                
            } catch (error) {
                console.log(`   ❌ Error checking status: ${error.message}`);
            }
        }
        
        if (!callConnected) {
            console.log('');
            console.log('⚠️  CALL STATUS UNCLEAR');
            console.log('======================');
            console.log('📊 After 60 seconds of monitoring:');
            console.log('✅ Call was initiated successfully (no SIP 478 error)');
            console.log('❓ Participant not detected in LiveKit room');
            console.log('');
            console.log('🔍 POSSIBLE REASONS:');
            console.log('1. Call is ringing but not answered yet');
            console.log('2. Call went to voicemail');
            console.log('3. Phone is busy or unreachable');
            console.log('4. Network delay in status reporting');
            console.log('');
            console.log('💡 RECOMMENDATIONS:');
            console.log('1. Check if you received the phone call');
            console.log('2. Answer the call if it\'s still ringing');
            console.log('3. Try calling a different number to test');
            console.log('4. Check Stringee dashboard for call logs');
        }
        
        return participant;
        
    } catch (error) {
        console.error('❌ CALL FAILED:', error.message);
        
        if (error.message.includes('478')) {
            console.error('');
            console.error('🔍 SIP 478 ERROR ANALYSIS:');
            console.error('==========================');
            console.error('❌ "Unresolvable destination" error returned');
            console.error('');
            console.error('💡 POSSIBLE CAUSES:');
            console.error('1. Phone number routing issue with Stringee');
            console.error('2. International calling restrictions');
            console.error('3. Insufficient account balance');
            console.error('4. Trunk configuration changed');
            console.error('');
            console.error('🔧 IMMEDIATE ACTIONS:');
            console.error('1. Check Stringee account balance');
            console.error('2. Verify international calling is enabled');
            console.error('3. Test with a different phone number');
            console.error('4. Contact Stringee support');
        }
        
        return null;
    }
}

// Main function
async function main() {
    console.log('🚀 LiveKit SIP Final Test');
    console.log('=========================\n');
    
    const result = await finalCallTest();
    
    console.log('\n' + '='.repeat(50));
    console.log('📋 FINAL TEST SUMMARY');
    console.log('='.repeat(50));
    
    if (result) {
        console.log('✅ Status: SUCCESS');
        console.log('✅ SIP 478 error: RESOLVED');
        console.log('✅ Call initiation: WORKING');
        console.log('✅ Configuration: OPTIMIZED');
        console.log('');
        console.log('🎯 READY FOR PRODUCTION USE!');
        console.log('');
        console.log('📝 Use this configuration:');
        console.log('- Phone format: +***********');
        console.log('- waitUntilAnswered: false');
        console.log('- playDialtone: true');
        console.log('- krispEnabled: false');
        
    } else {
        console.log('❌ Status: FAILED');
        console.log('❌ SIP 478 error: STILL OCCURRING');
        console.log('❌ Call initiation: NOT WORKING');
        console.log('');
        console.log('🔧 NEXT STEPS:');
        console.log('1. Check Stringee account configuration');
        console.log('2. Verify account balance and permissions');
        console.log('3. Test with different phone numbers');
        console.log('4. Contact Stringee technical support');
    }
    
    console.log('\n🏁 Test completed at:', new Date().toLocaleString());
}

main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
});
