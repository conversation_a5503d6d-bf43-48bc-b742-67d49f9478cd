import { SipClient, RoomServiceClient } from 'livekit-server-sdk';

// C<PERSON><PERSON> hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo clients
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);
const roomService = new RoomServiceClient(livekitUrl, apiKey, apiSecret);

// Các format số điện thoại khác nhau để test
const phoneNumberFormats = [
    // Vietnam mobile formats
    '+84938709344',     // International format
    '84938709344',      // Without + prefix
    '0938709344',       // Local format with 0
    '938709344',        // Without leading 0
    
    // Stringee specific formats (c<PERSON> thể cần)
    'sip:+<EMAIL>',
    'sip:<EMAIL>',
    'sip:<EMAIL>',
    
    // Test với số Stringee trunk
    '+842473030026',    // Trunk number itself
    '842473030026',
    '02473030026'
];

// Hàm test từng format số điện thoại
async function testPhoneNumberFormats() {
    console.log('🧪 Testing different phone number formats for SIP 478 fix...');
    console.log('Target: Vietnam mobile number 0938709344\n');
    
    try {
        // Lấy trunk
        const trunks = await sipClient.listSipOutboundTrunk();
        if (trunks.length === 0) {
            console.log('❌ No SIP trunks found');
            return;
        }
        
        const trunk = trunks[0];
        console.log('📞 Using trunk:', trunk.name, '(' + trunk.sipTrunkId + ')');
        console.log('📞 Trunk address:', trunk.address);
        console.log('📞 Trunk numbers:', trunk.numbers.join(', '));
        console.log('');
        
        let successCount = 0;
        let workingFormats = [];
        
        for (let i = 0; i < phoneNumberFormats.length; i++) {
            const phoneNumber = phoneNumberFormats[i];
            const testRoomName = `test-format-${i}-${Date.now()}`;
            
            console.log(`\n📞 Test ${i + 1}/${phoneNumberFormats.length}: ${phoneNumber}`);
            console.log(`   Room: ${testRoomName}`);
            
            try {
                // Tạo room
                await roomService.createRoom({
                    name: testRoomName,
                    emptyTimeout: 60, // 1 minute for quick test
                    maxParticipants: 2
                });
                
                // Thử gọi với format này
                const participant = await sipClient.createSipParticipant(
                    trunk.sipTrunkId,
                    phoneNumber,
                    testRoomName,
                    {
                        participantIdentity: `test-${i}-${Date.now()}`,
                        participantName: `Test Format ${i + 1}`,
                        participantMetadata: `Testing ${phoneNumber}`,
                        krispEnabled: false,
                        waitUntilAnswered: false, // Quick test
                        playDialtone: false,
                        hidePhoneNumber: false
                    }
                );
                
                console.log(`   ✅ SUCCESS! Format accepted`);
                console.log(`   📋 Participant ID: ${participant.participantId}`);
                console.log(`   📋 SIP Call ID: ${participant.sipCallId}`);
                
                successCount++;
                workingFormats.push({
                    format: phoneNumber,
                    participantId: participant.participantId,
                    sipCallId: participant.sipCallId,
                    roomName: testRoomName
                });
                
                // Đợi một chút để xem cuộc gọi có thành công không
                console.log('   ⏳ Waiting 5 seconds to check call status...');
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                // Kiểm tra participant trong room
                const participants = await roomService.listParticipants(testRoomName);
                if (participants.length > 0) {
                    const p = participants[0];
                    console.log(`   ✅ Call established! State: ${p.state}`);
                    console.log(`   📞 Connection quality: ${p.connectionQuality}`);
                } else {
                    console.log('   ⚠️  Call initiated but participant not in room (may still be connecting)');
                }
                
            } catch (error) {
                console.log(`   ❌ FAILED: ${error.message}`);
                
                // Phân tích lỗi cụ thể
                if (error.message.includes('478')) {
                    console.log('   → SIP 478: Unresolvable destination');
                } else if (error.message.includes('404')) {
                    console.log('   → SIP 404: Not found');
                } else if (error.message.includes('403')) {
                    console.log('   → SIP 403: Forbidden');
                } else if (error.message.includes('486')) {
                    console.log('   → SIP 486: Busy here');
                } else if (error.message.includes('408')) {
                    console.log('   → SIP 408: Request timeout');
                }
            }
            
            // Đợi giữa các test
            if (i < phoneNumberFormats.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // Tổng kết
        console.log('\n' + '='.repeat(50));
        console.log('📊 TEST RESULTS SUMMARY');
        console.log('='.repeat(50));
        console.log(`✅ Successful formats: ${successCount}/${phoneNumberFormats.length}`);
        
        if (workingFormats.length > 0) {
            console.log('\n🎉 WORKING FORMATS:');
            workingFormats.forEach((result, index) => {
                console.log(`${index + 1}. ${result.format}`);
                console.log(`   Room: ${result.roomName}`);
                console.log(`   SIP Call ID: ${result.sipCallId}`);
            });
            
            console.log('\n💡 RECOMMENDATION:');
            console.log(`Use format: ${workingFormats[0].format}`);
            console.log('This format was accepted by your SIP provider.');
            
        } else {
            console.log('\n❌ NO WORKING FORMATS FOUND');
            console.log('\n🔍 TROUBLESHOOTING STEPS:');
            console.log('1. Check Stringee account configuration');
            console.log('2. Verify phone number is reachable from Vietnam');
            console.log('3. Check if international calling is enabled');
            console.log('4. Verify trunk routing rules');
            console.log('5. Contact Stringee support for routing issues');
            
            console.log('\n📞 STRINGEE SPECIFIC CHECKS:');
            console.log('- Login to Stringee dashboard');
            console.log('- Check SIP trunk configuration');
            console.log('- Verify outbound calling permissions');
            console.log('- Check routing rules for Vietnam mobile numbers');
        }
        
    } catch (error) {
        console.error('❌ Error during phone number format testing:', error.message);
    }
}

// Hàm kiểm tra cấu hình Stringee trunk
async function analyzeStringeeTrunk() {
    console.log('\n🔍 Analyzing Stringee trunk configuration...');
    
    try {
        const trunks = await sipClient.listSipOutboundTrunk();
        if (trunks.length === 0) {
            console.log('❌ No trunks found');
            return;
        }
        
        const trunk = trunks[0];
        console.log('📋 Trunk Analysis:');
        console.log('- ID:', trunk.sipTrunkId);
        console.log('- Name:', trunk.name);
        console.log('- Address:', trunk.address);
        console.log('- Numbers:', trunk.numbers);
        console.log('- Auth Username:', trunk.authUsername);
        
        // Phân tích cấu hình
        const issues = [];
        const recommendations = [];
        
        // Kiểm tra address format
        if (!trunk.address.includes(':')) {
            issues.push('SIP address missing port number');
            recommendations.push('Add port number to SIP address (e.g., :5060 or :15060)');
        }
        
        // Kiểm tra Stringee specific
        if (trunk.address.includes('stringee.com')) {
            console.log('✅ Stringee SIP provider detected');
            recommendations.push('For Stringee, ensure your account has outbound calling enabled');
            recommendations.push('Check Stringee dashboard for routing rules');
            recommendations.push('Verify your Stringee account balance for outbound calls');
        }
        
        // Kiểm tra numbers format
        if (trunk.numbers && trunk.numbers.length > 0) {
            const number = trunk.numbers[0];
            if (number.startsWith('+842')) {
                console.log('✅ Vietnam landline number detected in trunk');
                recommendations.push('Vietnam landline may have restrictions calling mobile numbers');
                recommendations.push('Check with Stringee about mobile calling permissions');
            }
        }
        
        if (issues.length > 0) {
            console.log('\n⚠️  Configuration Issues:');
            issues.forEach(issue => console.log('- ' + issue));
        }
        
        if (recommendations.length > 0) {
            console.log('\n💡 Recommendations:');
            recommendations.forEach(rec => console.log('- ' + rec));
        }
        
    } catch (error) {
        console.error('❌ Error analyzing trunk:', error.message);
    }
}

// Main function
async function main() {
    console.log('🔧 LiveKit SIP 478 Error Fix Tool');
    console.log('==================================\n');
    
    await analyzeStringeeTrunk();
    await testPhoneNumberFormats();
    
    console.log('\n✅ Fix attempt completed');
    console.log('\n📞 Next steps:');
    console.log('1. If any format worked, use that format for your calls');
    console.log('2. If no format worked, check Stringee account configuration');
    console.log('3. Contact Stringee support if routing issues persist');
}

main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
});
