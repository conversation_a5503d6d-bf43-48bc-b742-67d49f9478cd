import { SipClient, RoomServiceClient } from 'livekit-server-sdk';

// <PERSON><PERSON>u hình LiveKit
const livekitUrl = 'wss://loma-vyn5765a.livekit.cloud';
const apiKey = 'APIggS5CXUXtwWk';
const apiSecret = 'ojfzTKqfgUrFWpjPe8R70tqbdwKmLEzROL3nwUTeDKRC';

// Tạo clients
const sipClient = new SipClient(livekitUrl, apiKey, apiSecret);
const roomService = new RoomServiceClient(livekitUrl, apiKey, apiSecret);

const phoneNumber = '+84938709344';
const roomName = 'real-monitor-' + Date.now();

async function realCallWithExtendedMonitoring() {
    console.log('📞 REAL CALL TEST WITH EXTENDED MONITORING');
    console.log('==========================================');
    console.log('📱 Target phone:', phoneNumber);
    console.log('🏠 Room name:', roomName);
    console.log('⏰ Start time:', new Date().toLocaleString());
    console.log('');
    
    try {
        // Lấy trunk
        const trunks = await sipClient.listSipOutboundTrunk();
        const trunk = trunks[0];
        
        console.log('📞 Using trunk:', trunk.name);
        console.log('📞 From number:', trunk.numbers[0]);
        console.log('');
        
        // Tạo room
        await roomService.createRoom({
            name: roomName,
            emptyTimeout: 900, // 15 minutes
            maxParticipants: 5
        });
        
        console.log('✅ Room created');
        console.log('');
        
        // Cấu hình cuộc gọi
        const callOptions = {
            participantIdentity: 'real-monitor-' + Date.now(),
            participantName: 'Real Call Monitor',
            participantMetadata: 'Extended monitoring test',
            krispEnabled: false,
            waitUntilAnswered: false,  // Không đợi answer
            playDialtone: true,
            hidePhoneNumber: false
        };
        
        console.log('🔔 IMPORTANT: PREPARE YOUR PHONE NOW!');
        console.log('📱 You should receive a call from:', trunk.numbers[0]);
        console.log('📱 Target number:', phoneNumber);
        console.log('');
        console.log('⏳ Starting call in 3 seconds...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Khởi tạo cuộc gọi
        console.log('📞 Initiating call...');
        const startTime = Date.now();
        
        const participant = await sipClient.createSipParticipant(
            trunk.sipTrunkId,
            phoneNumber,
            roomName,
            callOptions
        );
        
        const initTime = Date.now() - startTime;
        
        console.log('🎉 Call initiated successfully in', initTime + 'ms');
        console.log('📋 Participant ID:', participant.participantId);
        console.log('📋 SIP Call ID:', participant.sipCallId);
        console.log('');
        
        // Extended monitoring - 5 minutes total
        console.log('📊 EXTENDED CALL MONITORING (5 minutes)');
        console.log('=======================================');
        console.log('🔔 CHECK YOUR PHONE - IT SHOULD BE RINGING NOW!');
        console.log('');
        
        let callConnected = false;
        let lastStatus = 'unknown';
        const monitoringDuration = 300; // 5 minutes in seconds
        const checkInterval = 10; // Check every 10 seconds
        const totalChecks = monitoringDuration / checkInterval;
        
        for (let i = 1; i <= totalChecks; i++) {
            const elapsedTime = i * checkInterval;
            
            console.log(`⏰ Check ${i}/${totalChecks} - ${elapsedTime}s elapsed`);
            
            try {
                // Kiểm tra participants trong room
                const participants = await roomService.listParticipants(roomName);
                const sipParticipant = participants.find(p => p.identity === participant.participantIdentity);
                
                if (sipParticipant) {
                    if (!callConnected) {
                        console.log('🎉 CALL CONNECTED!');
                        console.log('================');
                        callConnected = true;
                    }
                    
                    const currentStatus = sipParticipant.state;
                    if (currentStatus !== lastStatus) {
                        console.log('📊 Status changed:', lastStatus, '→', currentStatus);
                        lastStatus = currentStatus;
                    }
                    
                    console.log('✅ Participant in room:');
                    console.log('   - State:', sipParticipant.state);
                    console.log('   - Connection quality:', sipParticipant.connectionQuality);
                    console.log('   - Is publisher:', sipParticipant.isPublisher);
                    console.log('   - Tracks:', sipParticipant.tracks?.length || 0);
                    
                    if (sipParticipant.tracks && sipParticipant.tracks.length > 0) {
                        console.log('   - Track details:');
                        sipParticipant.tracks.forEach(track => {
                            console.log(`     • ${track.type}: ${track.name} (${track.source})`);
                        });
                    }
                    
                } else {
                    console.log('⏳ Still waiting for connection...');
                    
                    // Thông báo đặc biệt tại các mốc thời gian
                    if (elapsedTime === 30) {
                        console.log('💡 Call should be ringing on your phone by now');
                        console.log('📱 Please check your phone and answer if ringing');
                    } else if (elapsedTime === 60) {
                        console.log('❓ Has your phone rung yet?');
                        console.log('   - If YES: Answer the call to test connection');
                        console.log('   - If NO: There may be a routing issue');
                    } else if (elapsedTime === 120) {
                        console.log('⚠️  2 minutes elapsed - call may have gone to voicemail');
                    } else if (elapsedTime === 180) {
                        console.log('⚠️  3 minutes elapsed - checking for any issues...');
                    }
                }
                
                // Kiểm tra room status
                const rooms = await roomService.listRooms();
                const currentRoom = rooms.find(r => r.name === roomName);
                if (currentRoom) {
                    console.log('🏠 Room status:');
                    console.log('   - Participants:', currentRoom.numParticipants);
                    console.log('   - Publishers:', currentRoom.numPublishers);
                }
                
            } catch (error) {
                console.log('❌ Error during monitoring:', error.message);
            }
            
            console.log(''); // Empty line for readability
            
            // Đợi trước khi check tiếp
            if (i < totalChecks) {
                await new Promise(resolve => setTimeout(resolve, checkInterval * 1000));
            }
        }
        
        // Kết luận sau 5 phút
        console.log('📋 FINAL RESULTS AFTER 5 MINUTES');
        console.log('=================================');
        
        if (callConnected) {
            console.log('🎉 SUCCESS: Call was connected!');
            console.log('✅ Your phone received the call');
            console.log('✅ LiveKit SIP integration is working');
            console.log('✅ Stringee routing is functional');
            console.log('');
            console.log('🎯 READY FOR PRODUCTION USE!');
        } else {
            console.log('⚠️  INCONCLUSIVE: Call initiated but no connection detected');
            console.log('');
            console.log('🔍 POSSIBLE REASONS:');
            console.log('1. Phone call was made but not answered');
            console.log('2. Call went to voicemail');
            console.log('3. Phone was busy or unreachable');
            console.log('4. Stringee account restrictions');
            console.log('5. Network/routing delays');
            console.log('');
            console.log('❓ IMPORTANT QUESTION:');
            console.log('Did you receive ANY phone call during this test?');
            console.log('- If YES: The system is working, just need to answer');
            console.log('- If NO: Check Stringee account configuration');
        }
        
        return { participant, callConnected };
        
    } catch (error) {
        console.error('❌ Call failed:', error.message);
        
        if (error.message.includes('478')) {
            console.error('🔍 SIP 478 error - this should not happen based on previous tests');
        }
        
        return null;
    }
}

// Hàm để kiểm tra Stringee account status
async function checkStringeeAccountStatus() {
    console.log('\n🔍 STRINGEE ACCOUNT STATUS CHECK');
    console.log('===============================');
    
    console.log('💡 To check your Stringee account:');
    console.log('1. Login to: https://developer.stringee.com/');
    console.log('2. Check account balance');
    console.log('3. Verify outbound calling is enabled');
    console.log('4. Check call logs for recent attempts');
    console.log('5. Verify mobile calling permissions');
    console.log('');
    console.log('📞 Common Stringee Issues:');
    console.log('- Insufficient balance for outbound calls');
    console.log('- Mobile calling restrictions from landline numbers');
    console.log('- International calling not enabled');
    console.log('- Routing rules not configured for Vietnam mobile');
}

// Main function
async function main() {
    console.log('🚀 REAL CALL MONITORING TEST');
    console.log('============================\n');
    
    const result = await realCallWithExtendedMonitoring();
    
    await checkStringeeAccountStatus();
    
    console.log('\n🏁 Test completed at:', new Date().toLocaleString());
    
    if (result && result.callConnected) {
        console.log('\n✅ CONCLUSION: System is working correctly!');
    } else {
        console.log('\n⚠️  CONCLUSION: Need to verify Stringee account settings');
    }
}

main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
});
